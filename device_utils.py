# device_utils.py
import uiautomator2 as u2
import os
import re

# 全局存储设备连接[U+3001]日志路径和设备名称
current_device = None
current_log_path = None
current_device_name = None  # 新增全局变量存储设备名称

def connect_device(sb_url, sb_name, sb_url_params=None):
    """
    连接设备并初始化日志路径
    :param sb_url: 设备连接地址
    :param sb_name: 设备名称
    :param sb_url_params: URL参数[U+FF08]用于生成唯一日志路径[U+FF09]
    :return: 设备实例和日志路径
    """
    global current_device, current_log_path, current_device_name
    
    # 连接设备
    try:
        current_device = u2.connect(sb_url)
        current_device_name = sb_name  # 存储设备名称
        print(f"[成功] 成功连接设备 {sb_name}")
        print(current_device.info)
    except Exception as e:
        print(f"[错误] 设备连接失败: {str(e)}")
        raise

    
    
    current_log_path = ensure_directory_exists(os.getcwd(), sb_name)
    return current_device, current_log_path

def get_device():
    """获取当前设备实例"""
    if not current_device:
        raise RuntimeError("设备未初始化[U+FF0C]请先调用connect_device")
    return current_device

def get_log_path():
    """获取当前日志路径"""
    if not current_log_path:
        raise RuntimeError("日志路径未初始化[U+FF0C]请先调用connect_device")
    return current_log_path

def get_device_name():
    """获取当前连接的设备名称"""
    if not current_device_name:
        raise RuntimeError("设备名称未初始化[U+FF0C]请先调用connect_device")
    return current_device_name

def ensure_directory_exists(project_path, folder_name):
    """
    确保项目路径下存在指定的文件夹[U+FF0C]如果不存在则创建

    参数:
        project_path (str): 项目根目录路径
        folder_name (str): 要检查/创建的文件夹名称

    返回:
        str: 最终确定的文件夹完整路径
    """
    folder_path = os.path.join(project_path, folder_name)

    if not os.path.exists(folder_path):
        try:
            os.makedirs(folder_path)
            print(f"文件夹 '{folder_name}' 已创建于: {folder_path}")
        except OSError as e:
            print(f"创建文件夹 '{folder_name}' 失败: {e}")
    else:
        print(f"文件夹 '{folder_name}' 已存在: {folder_path}")

    return folder_path